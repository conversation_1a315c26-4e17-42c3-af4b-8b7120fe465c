{"name": "atma-backend-integration-tests", "version": "1.0.0", "description": "Integration tests and utilities for ATMA Backend services", "main": "test-integration-fix.js", "scripts": {"test": "node test-integration-fix.js", "test:integration": "node test-integration-fix.js", "rollback": "node rollback-integration-fix.js"}, "dependencies": {"axios": "^1.6.0", "uuid": "^9.0.0"}, "devDependencies": {}, "keywords": ["atma", "integration", "testing", "microservices"], "author": "ATMA Development Team", "license": "ISC"}