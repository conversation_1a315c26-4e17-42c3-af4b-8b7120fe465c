# Assessment-Archive Service Integration Fix Plan

## Overview
Plan perbaikan untuk memperbaiki interaksi antara Assessment Service dan Archive Service berdasarkan perubahan API Archive Service yang baru.

## Current Issues

### 1. **Port Configuration Conflict**
- **Problem**: Archive Service dokumentasi menunjukkan port 3003, tapi Assessment Service juga menggunakan port 3003
- **Impact**: Service conflict dan connection errors
- **Solution**: Pastikan Archive Service menggunakan port 3002

### 2. **Authentication Headers Inconsistency** 
- **Problem**: Assessment Service menggunakan `X-Service-Key` + `X-Internal-Service`, Archive Service API baru menggunakan `X-API-Key`
- **Impact**: Authentication failures
- **Solution**: Tetap gunakan `X-Service-Key` + `X-Internal-Service` untuk konsistensi internal service

### 3. **Endpoint Path Duplication**
- **Problem**: Assessment Service memanggil `/archive/jobs/${jobId}/status`, tapi base URL sudah include `/archive`
- **Impact**: Path menjadi `/archive/archive/jobs/...` (404 errors)
- **Solution**: Update endpoint paths untuk menghilangkan duplikasi

### 4. **Missing Job Creation in Assessment Service**
- **Problem**: Job tidak dibuat di Archive Service saat submit, hanya di local tracker
- **Impact**: Inconsistent job tracking, status sync failures
- **Solution**: Create job di Archive Service saat submit assessment

---

## Implementation Plan

### **Phase 1: Fix Critical Configuration Issues** ⚡

#### Task 1.1: Fix Archive Service Port Configuration
**File**: `archive-service/API_DOCUMENTATION.md`
**Change**: Update base URL dari `http://localhost:3003/archive` ke `http://localhost:3002/archive`

#### Task 1.2: Update Assessment Service Base URL
**File**: `assessment-service/src/services/archiveService.js`
**Change**: 
```javascript
// BEFORE
const ARCHIVE_SERVICE_URL = process.env.ARCHIVE_SERVICE_URL || 'http://localhost:3002';

// AFTER  
const ARCHIVE_SERVICE_URL = process.env.ARCHIVE_SERVICE_URL || 'http://localhost:3002/archive';
```

#### Task 1.3: Fix Endpoint Paths
**File**: `assessment-service/src/services/archiveService.js`
**Change**:
```javascript
// BEFORE
await archiveClient.put(`/archive/jobs/${jobId}/status`, {...})
await archiveClient.get(`/archive/jobs/${jobId}`)

// AFTER
await archiveClient.put(`/jobs/${jobId}/status`, {...})
await archiveClient.get(`/jobs/${jobId}`)
```

### **Phase 2: Improve Job Creation Flow** 🔄

#### Task 2.1: Add createJob Method to Assessment Service
**File**: `assessment-service/src/services/archiveService.js`
**Add new method**:
```javascript
/**
 * Create job in Archive Service
 * @param {String} jobId - Job ID
 * @param {String} userId - User ID  
 * @param {Object} assessmentData - Assessment data
 * @returns {Promise<Object>} - Created job
 */
const createJob = async (jobId, userId, assessmentData) => {
  try {
    const response = await archiveClient.post('/jobs', {
      job_id: jobId,
      user_id: userId,
      assessment_data: assessmentData,
      status: 'queued'
    });
    
    logger.info('Job created in Archive Service', { jobId, userId });
    return response.data.data;
  } catch (error) {
    logger.error('Failed to create job in Archive Service', {
      jobId, userId, error: error.message
    });
    throw error;
  }
};
```

#### Task 2.2: Update Assessment Submit Flow
**File**: `assessment-service/src/routes/assessments.js`
**Modify submit endpoint**:
```javascript
// Generate job ID
const jobId = uuidv4();

// Deduct tokens first
await authService.deductTokens(userId, token, tokenCost);

// Create job in Archive Service
try {
  await archiveService.createJob(jobId, userId, assessmentData);
} catch (archiveError) {
  // Refund tokens if job creation fails
  await authService.refundTokens(userId, token, tokenCost);
  throw new AppError('ARCHIVE_SERVICE_ERROR', 'Failed to create job in archive service', 503);
}

// Create job in local tracker
jobTracker.createJob(jobId, userId, userEmail, assessmentData);

// Publish to queue
await queueService.publishAssessmentJob(assessmentData, userId, userEmail, jobId);
```

### **Phase 3: Update Analysis Worker** 🔧

#### Task 3.1: Remove Job Creation from Analysis Worker
**File**: `analysis-worker/src/processors/assessmentProcessor.js`
**Remove**:
```javascript
// REMOVE THIS STEP
// Step 0: Create job entry in database
await archiveService.createAnalysisJob(jobId, userId, assessmentData);
```

#### Task 3.2: Update Analysis Worker Archive Service
**File**: `analysis-worker/src/services/archiveService.js`
**Changes**:
1. Update base URL untuk include `/archive` prefix
2. Keep authentication headers consistent (`X-Service-Key` + `X-Internal-Service`)
3. Update endpoint paths
4. Remove `createAnalysisJob` method (tidak diperlukan lagi)

```javascript
// Update configuration
const config = {
  baseURL: process.env.ARCHIVE_SERVICE_URL || 'http://localhost:3002/archive',
  serviceKey: process.env.INTERNAL_SERVICE_KEY,
  timeout: 30000
};

// Update headers (keep consistent)
headers: {
  'Content-Type': 'application/json',
  'X-Internal-Service': 'true',
  'X-Service-Key': config.serviceKey
}
```

#### Task 3.3: Update Processing Flow
**File**: `analysis-worker/src/processors/assessmentProcessor.js`
**New flow**:
```javascript
const processAssessment = async (jobData) => {
  const { jobId, userId, userEmail, assessmentData } = jobData;
  
  try {
    // Step 1: Update job status to processing (job already exists)
    await archiveService.updateAnalysisJobStatus(jobId, 'processing');

    // Step 2: Generate persona profile using AI
    const personaProfile = await aiService.generatePersonaProfile(assessmentData, jobId);
    
    // Step 3: Save result to Archive Service
    const saveResult = await archiveService.saveAnalysisResult(userId, assessmentData, personaProfile, jobId);
    
    // Step 4: Update job status to completed with result_id
    await archiveService.updateAnalysisJobStatus(jobId, 'completed', {
      result_id: saveResult.id
    });

    // Step 5: Notify Assessment Service
    await notificationService.updateAssessmentJobStatus(jobId, saveResult.id, 'completed');

    return saveResult;
  } catch (error) {
    // Handle errors and update job status to failed
    await archiveService.updateAnalysisJobStatus(jobId, 'failed', {
      error_message: error.message
    });
    
    // Notify Assessment Service about failure
    await notificationService.updateAssessmentJobStatus(jobId, null, 'failed', error.message);
    
    throw error;
  }
};
```

---

## New Interaction Flow

### **Before (Current)**:
1. User submit assessment → Assessment Service
2. Assessment Service: Deduct tokens, create local job, publish to queue
3. Analysis Worker: Create job in Archive Service, process, save result
4. Analysis Worker: Callback to Assessment Service

### **After (Improved)**:
1. User submit assessment → Assessment Service  
2. Assessment Service: Deduct tokens, **create job in Archive Service**, create local job, publish to queue
3. Analysis Worker: Update job to processing, process, save result, update job to completed
4. Analysis Worker: Callback to Assessment Service

### **Key Improvements**:
- **Earlier Job Creation**: Job dibuat saat submit, bukan saat processing
- **Better Tracking**: Consistent job tracking antara local dan archive
- **Improved Error Handling**: Refund tokens jika job creation gagal
- **Cleaner Separation**: Assessment Service handle job creation, Analysis Worker handle processing

---

## Testing Strategy

### **Phase 1 Testing**:
1. Test health check endpoints
2. Test authentication dengan Archive Service
3. Verify endpoint paths working correctly

### **Phase 2 Testing**:
1. Test job creation saat submit assessment
2. Test error handling jika Archive Service down
3. Test token refund jika job creation gagal

### **Phase 3 Testing**:
1. Test end-to-end flow dari submit sampai completion
2. Test Analysis Worker processing dengan job yang sudah ada
3. Test error scenarios dan callback notifications

### **Integration Testing**:
1. Test complete flow dengan semua services running
2. Test concurrent job submissions
3. Test service restart scenarios
4. Monitor logs untuk authentication dan connection issues

---

## Risk Mitigation

1. **Backup Strategy**: Backup current working code sebelum changes
2. **Incremental Deployment**: Deploy changes secara bertahap per phase
3. **Monitoring**: Monitor authentication failures dan API errors
4. **Rollback Plan**: Siapkan rollback procedure jika ada critical issues
5. **Environment Variables**: Ensure semua services memiliki config yang benar

---

## Analysis Worker Detailed Changes

### **Tujuan Perubahan Analysis Worker**:
1. **Simplify Job Management**: Worker tidak lagi bertanggung jawab create job, fokus ke processing
2. **Improve Error Handling**: Better error handling dengan job status yang sudah ada
3. **Consistent API Usage**: Gunakan endpoint dan authentication yang konsisten
4. **Better Separation of Concerns**: Assessment Service handle job lifecycle, Worker handle processing

### **Yang Harus Diperhatikan**:
1. **Job Already Exists**: Worker harus assume job sudah ada di Archive Service
2. **Status Transitions**: Pastikan status transition yang benar (queued → processing → completed/failed)
3. **Error Recovery**: Jika job tidak ditemukan, handle gracefully
4. **Callback Consistency**: Pastikan callback ke Assessment Service tetap berfungsi
5. **Retry Logic**: Update retry logic untuk tidak create job lagi

### **Specific File Changes in Analysis Worker**:

#### **File**: `src/services/archiveService.js`
**Changes**:
```javascript
// 1. Update base URL configuration
const config = {
  baseURL: process.env.ARCHIVE_SERVICE_URL || 'http://localhost:3002/archive',
  serviceKey: process.env.INTERNAL_SERVICE_KEY,
  timeout: 30000
};

// 2. Keep authentication headers consistent
const archiveClient = axios.create({
  baseURL: config.baseURL,
  timeout: config.timeout,
  headers: {
    'Content-Type': 'application/json',
    'X-Internal-Service': 'true',
    'X-Service-Key': config.serviceKey
  }
});

// 3. Update endpoint paths (remove /archive prefix)
const updateAnalysisJobStatus = async (jobId, status, additionalData = {}) => {
  // BEFORE: `/archive/jobs/${jobId}/status`
  // AFTER: `/jobs/${jobId}/status`
  const response = await archiveClient.put(`/jobs/${jobId}/status`, {
    status,
    ...additionalData
  });
};

const saveAnalysisResult = async (userId, assessmentData, personaProfile, jobId) => {
  // BEFORE: `/archive/results`
  // AFTER: `/results`
  const response = await archiveClient.post('/results', requestBody);
};

// 4. REMOVE createAnalysisJob method completely
// This method is no longer needed since Assessment Service creates the job
```

#### **File**: `src/processors/assessmentProcessor.js`
**Changes**:
```javascript
const processAssessment = async (jobData) => {
  const { jobId, userId, userEmail, assessmentData } = jobData;

  try {
    logger.info('Starting assessment processing', { jobId, userId });

    // REMOVE: Step 0 - Create job (job already exists from Assessment Service)
    // await archiveService.createAnalysisJob(jobId, userId, assessmentData);

    // Step 1: Update job status to processing (job already exists)
    await archiveService.updateAnalysisJobStatus(jobId, 'processing');

    // Step 2: Generate persona profile using AI (unchanged)
    const personaProfile = await withRetry(
      () => aiService.generatePersonaProfile(assessmentData, jobId),
      { operationName: 'AI persona generation' }
    );

    // Step 3: Save result to Archive Service (unchanged)
    const saveResult = await withRetry(
      () => archiveService.saveAnalysisResult(userId, assessmentData, personaProfile, jobId),
      { operationName: 'Archive service save' }
    );

    // Step 4: Update job status to completed with result_id (unchanged)
    await archiveService.updateAnalysisJobStatus(jobId, 'completed', {
      result_id: saveResult.id
    });

    // Step 5: Notify Assessment Service (unchanged)
    await notificationService.updateAssessmentJobStatus(jobId, saveResult.id, 'completed');

    return saveResult;

  } catch (error) {
    logger.error('Assessment processing failed', { jobId, userId, error: error.message });

    // Handle failure: Update job status and notify Assessment Service
    try {
      await archiveService.updateAnalysisJobStatus(jobId, 'failed', {
        error_message: error.message
      });
    } catch (statusError) {
      logger.error('Failed to update job status to failed', { jobId, error: statusError.message });
    }

    // Save failed result and notify Assessment Service
    let failedResultId = null;
    try {
      const failedResult = await archiveService.saveFailedAnalysisResult(
        userId, assessmentData, error.message, jobId
      );
      failedResultId = failedResult.id;
    } catch (saveError) {
      logger.error('Failed to save failed result', { jobId, error: saveError.message });
    }

    // Notify Assessment Service about failure
    await notificationService.updateAssessmentJobStatus(jobId, failedResultId, 'failed', error.message);

    throw error;
  }
};
```

### **Error Handling Improvements**:
1. **Job Not Found**: Jika job tidak ditemukan saat update status, log warning tapi jangan fail
2. **Archive Service Down**: Implement proper retry logic untuk Archive Service calls
3. **Graceful Degradation**: Jika Archive Service down, tetap process dan callback ke Assessment Service
4. **Status Consistency**: Pastikan status di Archive Service dan Assessment Service selalu sync

---

## Environment Variables Update

### **Assessment Service** (`.env`):
```env
# Archive Service Configuration
ARCHIVE_SERVICE_URL=http://localhost:3002/archive
INTERNAL_SERVICE_KEY=internal_service_secret_key_change_in_production
```

### **Analysis Worker** (`.env`):
```env
# Archive Service Configuration
ARCHIVE_SERVICE_URL=http://localhost:3002/archive
INTERNAL_SERVICE_KEY=internal_service_secret_key_change_in_production
```

### **Archive Service** (`.env`):
```env
# Server Configuration
PORT=3002
INTERNAL_SERVICE_KEY=internal_service_secret_key_change_in_production
```
