/**
 * Rollback Script for Assessment-Archive Service Integration Fix
 * 
 * This script reverts the changes made by the integration fix
 * Use this if critical issues are encountered after deployment
 */

const fs = require('fs');
const path = require('path');

// Backup configurations (original values)
const ROLLBACK_CHANGES = {
  'assessment-service/src/services/archiveService.js': {
    baseURL: 'http://localhost:3002',
    endpoints: {
      '/jobs/': '/archive/jobs/',
      'post(\'/jobs\'': 'post(\'/archive/jobs\'',
    },
    removeCreateJob: true
  },
  'analysis-worker/src/services/archiveService.js': {
    baseURL: 'http://localhost:3002',
    endpoints: {
      '/jobs': '/archive/jobs',
      '/results': '/archive/results'
    },
    restoreCreateAnalysisJob: true
  },
  'analysis-worker/src/processors/assessmentProcessor.js': {
    restoreJobCreation: true
  },
  'assessment-service/src/routes/assessments.js': {
    removeArchiveJobCreation: true
  },
  'assessment-service/src/routes/test.js': {
    removeArchiveJobCreation: true
  },
  'archive-service/API_DOCUMENTATION.md': {
    port: '3003',
    authHeaders: 'X-API-Key'
  },
  'assessment-service/.env.example': {
    removeArchiveServiceUrl: true
  },
  'analysis-worker/.env.example': {
    archiveServiceUrl: 'http://localhost:3002'
  }
};

function logStep(step, message) {
  console.log(`\n[${step}] ${message}`);
}

function logSuccess(message) {
  console.log(`✅ ${message}`);
}

function logError(message, error) {
  console.log(`❌ ${message}`);
  if (error) {
    console.log(`   Error: ${error.message || error}`);
  }
}

function logWarning(message) {
  console.log(`⚠️  ${message}`);
}

function createBackup(filePath) {
  const backupPath = `${filePath}.backup-${Date.now()}`;
  try {
    if (fs.existsSync(filePath)) {
      fs.copyFileSync(filePath, backupPath);
      logSuccess(`Created backup: ${backupPath}`);
      return backupPath;
    }
  } catch (error) {
    logError(`Failed to create backup for ${filePath}`, error);
  }
  return null;
}

function rollbackAssessmentServiceArchiveService() {
  logStep('1', 'Rolling back Assessment Service archiveService.js');
  
  const filePath = 'assessment-service/src/services/archiveService.js';
  
  try {
    createBackup(filePath);
    
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Revert base URL
    content = content.replace(
      'http://localhost:3002/archive',
      'http://localhost:3002'
    );
    
    // Revert endpoint paths
    content = content.replace(/\/jobs\//g, '/archive/jobs/');
    content = content.replace('post(\'/jobs\'', 'post(\'/archive/jobs\'');
    
    // Remove createJob method
    const createJobRegex = /\/\*\*\s*\* Create job in Archive Service[\s\S]*?\};/;
    content = content.replace(createJobRegex, '');
    
    // Remove createJob from exports
    content = content.replace(/,\s*createJob/, '');
    
    fs.writeFileSync(filePath, content);
    logSuccess('Assessment Service archiveService.js rolled back');
    
  } catch (error) {
    logError('Failed to rollback Assessment Service archiveService.js', error);
  }
}

function rollbackAnalysisWorkerArchiveService() {
  logStep('2', 'Rolling back Analysis Worker archiveService.js');
  
  const filePath = 'analysis-worker/src/services/archiveService.js';
  
  try {
    createBackup(filePath);
    
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Revert base URL
    content = content.replace(
      'http://localhost:3002/archive',
      'http://localhost:3002'
    );
    
    // Revert endpoint paths
    content = content.replace(/post\('\/results'/g, 'post(\'/archive/results\'');
    content = content.replace(/put\('\/results\//g, 'put(\'/archive/results/');
    content = content.replace(/post\('\/jobs'/g, 'post(\'/archive/jobs\'');
    content = content.replace(/put\('\/jobs\//g, 'put(\'/archive/jobs/');
    
    // Restore createAnalysisJob method
    const createAnalysisJobMethod = `
/**
 * Create analysis job in Archive Service
 * @param {String} jobId - Job ID
 * @param {String} userId - User ID
 * @param {Object} assessmentData - Assessment data
 * @returns {Promise<Object|null>} - Created job or null if failed
 */
const createAnalysisJob = async (jobId, userId, assessmentData) => {
  try {
    logger.info('Creating analysis job', { jobId, userId });

    const requestBody = {
      job_id: jobId,
      user_id: userId,
      assessment_data: assessmentData,
      status: 'processing'
    };

    const response = await archiveClient.post('/archive/jobs', requestBody);

    logger.info('Analysis job created successfully', {
      jobId,
      userId,
      jobDbId: response.data.data.id
    });

    return response.data.data;
  } catch (error) {
    logger.error('Failed to create analysis job', {
      jobId,
      userId,
      error: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText
    });
    // Don't throw error - allow processing to continue
    return null;
  }
};`;

    // Replace the comment with the actual method
    content = content.replace(
      '// createAnalysisJob method removed - jobs are now created by Assessment Service',
      createAnalysisJobMethod
    );
    
    // Add createAnalysisJob back to exports
    content = content.replace(
      'module.exports = {\n  saveAnalysisResult,\n  saveFailedAnalysisResult,\n  updateAnalysisResult,\n  checkHealth,\n  updateAnalysisJobStatus\n};',
      'module.exports = {\n  saveAnalysisResult,\n  saveFailedAnalysisResult,\n  updateAnalysisResult,\n  checkHealth,\n  createAnalysisJob,\n  updateAnalysisJobStatus\n};'
    );
    
    fs.writeFileSync(filePath, content);
    logSuccess('Analysis Worker archiveService.js rolled back');
    
  } catch (error) {
    logError('Failed to rollback Analysis Worker archiveService.js', error);
  }
}

function rollbackAssessmentProcessor() {
  logStep('3', 'Rolling back Assessment Processor');
  
  const filePath = 'analysis-worker/src/processors/assessmentProcessor.js';
  
  try {
    createBackup(filePath);
    
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Restore job creation step
    content = content.replace(
      '    // Step 1: Update job status to processing (job already exists from Assessment Service)\n    await archiveService.updateAnalysisJobStatus(jobId, \'processing\');',
      '    // Step 0: Create job entry in database\n    await archiveService.createAnalysisJob(jobId, userId, assessmentData);\n\n    // Step 0.5: Update job status to processing\n    await archiveService.updateAnalysisJobStatus(jobId, \'processing\');'
    );
    
    // Revert step numbers
    content = content.replace(/Step 2:/g, 'Step 1:');
    content = content.replace(/Step 3:/g, 'Step 2:');
    content = content.replace(/Step 4:/g, 'Step 2.5:');
    content = content.replace(/Step 5:/g, 'Step 3:');
    
    fs.writeFileSync(filePath, content);
    logSuccess('Assessment Processor rolled back');
    
  } catch (error) {
    logError('Failed to rollback Assessment Processor', error);
  }
}

function rollbackAssessmentRoutes() {
  logStep('4', 'Rolling back Assessment Routes');
  
  const files = [
    'assessment-service/src/routes/assessments.js',
    'assessment-service/src/routes/test.js'
  ];
  
  files.forEach(filePath => {
    try {
      createBackup(filePath);
      
      let content = fs.readFileSync(filePath, 'utf8');
      
      // Remove Archive Service job creation
      const archiveJobCreationRegex = /\s*\/\/ Create job in Archive Service first[\s\S]*?}\s*}/;
      content = content.replace(archiveJobCreationRegex, '');
      
      // Restore simple job creation
      content = content.replace(
        '    // Generate job ID\n    const jobId = uuidv4();\n\n    // Create job in local tracker',
        '    // Generate job ID and create job\n    const jobId = uuidv4();\n\n    // Create job in tracker'
      );
      
      // Remove archiveService import from test.js
      if (filePath.includes('test.js')) {
        content = content.replace(/const archiveService = require\('\.\.\/services\/archiveService'\);\n/, '');
      }
      
      fs.writeFileSync(filePath, content);
      logSuccess(`${filePath} rolled back`);
      
    } catch (error) {
      logError(`Failed to rollback ${filePath}`, error);
    }
  });
}

function rollbackDocumentation() {
  logStep('5', 'Rolling back Documentation');
  
  const filePath = 'archive-service/API_DOCUMENTATION.md';
  
  try {
    createBackup(filePath);
    
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Revert port
    content = content.replace(
      'http://localhost:3002/archive',
      'http://localhost:3003/archive'
    );
    
    // Revert authentication headers
    content = content.replace(
      '- **Service Authentication**: `X-Service-Key` + `X-Internal-Service: true` headers untuk endpoint internal service',
      '- **Service Authentication**: API key untuk endpoint internal service'
    );
    
    content = content.replace(
      '   - Service endpoints: `X-Service-Key: <service_key>` + `X-Internal-Service: true`',
      '   - Service endpoints: `X-API-Key: <service_api_key>`'
    );
    
    fs.writeFileSync(filePath, content);
    logSuccess('API Documentation rolled back');
    
  } catch (error) {
    logError('Failed to rollback API Documentation', error);
  }
}

function rollbackEnvironmentFiles() {
  logStep('6', 'Rolling back Environment Files');
  
  // Assessment Service .env.example
  try {
    const assessmentEnvPath = 'assessment-service/.env.example';
    createBackup(assessmentEnvPath);
    
    let content = fs.readFileSync(assessmentEnvPath, 'utf8');
    
    // Remove Archive Service URL
    content = content.replace(/# Archive Service Configuration\nARCHIVE_SERVICE_URL=http:\/\/localhost:3002\/archive\n\n/, '');
    
    fs.writeFileSync(assessmentEnvPath, content);
    logSuccess('Assessment Service .env.example rolled back');
    
  } catch (error) {
    logError('Failed to rollback Assessment Service .env.example', error);
  }
  
  // Analysis Worker .env.example
  try {
    const workerEnvPath = 'analysis-worker/.env.example';
    createBackup(workerEnvPath);
    
    let content = fs.readFileSync(workerEnvPath, 'utf8');
    
    // Revert Archive Service URL
    content = content.replace(
      'ARCHIVE_SERVICE_URL=http://localhost:3002/archive',
      'ARCHIVE_SERVICE_URL=http://localhost:3002'
    );
    
    fs.writeFileSync(workerEnvPath, content);
    logSuccess('Analysis Worker .env.example rolled back');
    
  } catch (error) {
    logError('Failed to rollback Analysis Worker .env.example', error);
  }
}

function cleanupTestFiles() {
  logStep('7', 'Cleaning up Test Files');
  
  const testFiles = [
    'test-integration-fix.js',
    'rollback-integration-fix.js'
  ];
  
  testFiles.forEach(file => {
    try {
      if (fs.existsSync(file)) {
        fs.unlinkSync(file);
        logSuccess(`Removed ${file}`);
      }
    } catch (error) {
      logError(`Failed to remove ${file}`, error);
    }
  });
}

async function performRollback() {
  console.log('🔄 Rolling back Assessment-Archive Service Integration Fix');
  console.log('=========================================================');
  
  logWarning('This will revert all changes made by the integration fix');
  logWarning('Make sure to stop all services before running this rollback');
  
  // Perform rollback steps
  rollbackAssessmentServiceArchiveService();
  rollbackAnalysisWorkerArchiveService();
  rollbackAssessmentProcessor();
  rollbackAssessmentRoutes();
  rollbackDocumentation();
  rollbackEnvironmentFiles();
  cleanupTestFiles();
  
  console.log('\n✅ Rollback completed');
  console.log('\n📋 Next steps:');
  console.log('1. Restart all services');
  console.log('2. Verify services are working with original configuration');
  console.log('3. Check logs for any issues');
  console.log('4. Update environment variables if needed');
}

// Run rollback if this file is executed directly
if (require.main === module) {
  performRollback().catch(console.error);
}

module.exports = {
  performRollback,
  rollbackAssessmentServiceArchiveService,
  rollbackAnalysisWorkerArchiveService,
  rollbackAssessmentProcessor,
  rollbackAssessmentRoutes,
  rollbackDocumentation,
  rollbackEnvironmentFiles
};
