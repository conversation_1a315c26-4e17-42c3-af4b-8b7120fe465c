/**
 * Integration Test Script for Assessment-Archive Service Fix
 * 
 * This script tests the new integration flow:
 * 1. Assessment Service creates job in Archive Service
 * 2. Analysis Worker processes existing job
 * 3. End-to-end flow validation
 */

const axios = require('axios');
const { v4: uuidv4 } = require('uuid');

// Service URLs
const ASSESSMENT_SERVICE_URL = 'http://localhost:3003';
const ARCHIVE_SERVICE_URL = 'http://localhost:3002';
const AUTH_SERVICE_URL = 'http://localhost:3001';

// Test configuration
const INTERNAL_SERVICE_KEY = 'internal_service_secret_key_change_in_production';
const TEST_USER_EMAIL = '<EMAIL>';
const TEST_USER_PASSWORD = 'password123';

// Test data
const SAMPLE_ASSESSMENT = {
  riasec: {
    realistic: 75,
    investigative: 85,
    artistic: 60,
    social: 50,
    enterprising: 70,
    conventional: 55
  },
  ocean: {
    conscientiousness: 65,
    extraversion: 55,
    agreeableness: 45,
    neuroticism: 30,
    openness: 80
  },
  viaIs: {
    creativity: 85,
    curiosity: 78,
    judgment: 70,
    loveOfLearning: 82,
    perspective: 68,
    bravery: 75,
    perseverance: 80,
    honesty: 85,
    zest: 70,
    love: 65,
    kindness: 90,
    socialIntelligence: 75,
    teamwork: 80,
    fairness: 85,
    leadership: 70,
    forgiveness: 75,
    humility: 80,
    prudence: 75,
    selfRegulation: 70,
    appreciationOfBeauty: 85,
    gratitude: 90,
    hope: 85,
    humor: 75,
    spirituality: 60
  }
};

// Helper functions
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

const logStep = (step, message) => {
  console.log(`\n[${step}] ${message}`);
};

const logSuccess = (message) => {
  console.log(`✅ ${message}`);
};

const logError = (message, error) => {
  console.log(`❌ ${message}`);
  if (error) {
    console.log(`   Error: ${error.message || error}`);
  }
};

const logInfo = (message) => {
  console.log(`ℹ️  ${message}`);
};

// Test functions
async function testHealthChecks() {
  logStep('1', 'Testing Service Health Checks');

  let assessmentOk = false;
  let archiveOk = false;

  try {
    // Test Assessment Service
    const assessmentHealth = await axios.get(`${ASSESSMENT_SERVICE_URL}/health`);
    logSuccess(`Assessment Service: ${assessmentHealth.data.status}`);
    assessmentOk = true;
  } catch (error) {
    logError('Assessment Service health check failed', error);
  }

  try {
    // Test Archive Service
    const archiveHealth = await axios.get(`${ARCHIVE_SERVICE_URL}/health`);
    logSuccess(`Archive Service: ${archiveHealth.data.status}`);
    archiveOk = true;
  } catch (error) {
    logError('Archive Service health check failed', error);
  }

  try {
    // Test Auth Service (optional for integration test)
    const authHealth = await axios.get(`${AUTH_SERVICE_URL}/health`);
    logSuccess(`Auth Service: ${authHealth.data.status}`);
  } catch (error) {
    logInfo('Auth Service not available (skipping - not required for basic integration test)');
  }

  // Return true if both Assessment and Archive services are healthy
  return assessmentOk && archiveOk;
}

async function testArchiveServiceAuthentication() {
  logStep('2', 'Testing Archive Service Authentication');

  try {
    // Test with correct headers using a POST endpoint that requires service auth
    // We'll test with invalid data to get a validation error, but 401 means auth failed
    const response = await axios.post(`${ARCHIVE_SERVICE_URL}/archive/jobs`, {
      // Invalid data to trigger validation error (but should pass auth)
      job_id: 'test-job-id',
      user_id: 'test-user-id',
      assessment_data: {},
      status: 'queued'
    }, {
      headers: {
        'X-Service-Key': INTERNAL_SERVICE_KEY,
        'X-Internal-Service': 'true',
        'Content-Type': 'application/json'
      }
    });

    logSuccess('Archive Service authentication with service headers works');
    return true;
  } catch (error) {
    if (error.response?.status === 401) {
      logError('Archive Service authentication failed - check INTERNAL_SERVICE_KEY', error);
      return false;
    } else if (error.response?.status === 400) {
      // Validation error means auth passed but data was invalid - this is expected
      logSuccess('Archive Service authentication with service headers works (validation error expected)');
      return true;
    } else {
      logError('Archive Service authentication test failed', error);
      return false;
    }
  }
}

async function testJobCreationFlow() {
  logStep('3', 'Testing Job Creation Flow');
  
  try {
    // Submit test assessment
    const submitResponse = await axios.post(`${ASSESSMENT_SERVICE_URL}/test/submit`, SAMPLE_ASSESSMENT);
    
    if (submitResponse.status !== 202) {
      logError('Assessment submission failed', `Status: ${submitResponse.status}`);
      return null;
    }
    
    const { jobId } = submitResponse.data.data;
    logSuccess(`Assessment submitted successfully. Job ID: ${jobId}`);
    
    // Wait a moment for job creation
    await delay(1000);
    
    // Check if job exists in Archive Service
    try {
      const jobResponse = await axios.get(`${ARCHIVE_SERVICE_URL}/archive/jobs/${jobId}`, {
        headers: {
          'X-Service-Key': INTERNAL_SERVICE_KEY,
          'X-Internal-Service': 'true',
          'Content-Type': 'application/json'
        }
      });
      
      logSuccess(`Job found in Archive Service with status: ${jobResponse.data.data.status}`);
      return jobId;
    } catch (error) {
      if (error.response?.status === 404) {
        logError('Job not found in Archive Service - job creation may have failed');
      } else {
        logError('Failed to check job in Archive Service', error);
      }
      return null;
    }
    
  } catch (error) {
    logError('Job creation flow test failed', error);
    return null;
  }
}

async function testJobStatusUpdates(jobId) {
  logStep('4', 'Testing Job Status Updates');
  
  if (!jobId) {
    logError('No job ID provided for status update test');
    return false;
  }
  
  try {
    // Test updating job status
    const updateResponse = await axios.put(`${ARCHIVE_SERVICE_URL}/archive/jobs/${jobId}/status`, {
      status: 'processing'
    }, {
      headers: {
        'X-Service-Key': INTERNAL_SERVICE_KEY,
        'X-Internal-Service': 'true',
        'Content-Type': 'application/json'
      }
    });
    
    logSuccess(`Job status updated to: ${updateResponse.data.data.status}`);
    
    // Verify status update
    const jobResponse = await axios.get(`${ARCHIVE_SERVICE_URL}/archive/jobs/${jobId}`, {
      headers: {
        'X-Service-Key': INTERNAL_SERVICE_KEY,
        'X-Internal-Service': 'true',
        'Content-Type': 'application/json'
      }
    });
    
    if (jobResponse.data.data.status === 'processing') {
      logSuccess('Job status update verified');
      return true;
    } else {
      logError('Job status update verification failed', `Expected: processing, Got: ${jobResponse.data.data.status}`);
      return false;
    }
    
  } catch (error) {
    logError('Job status update test failed', error);
    return false;
  }
}

async function testEndToEndFlow() {
  logStep('5', 'Testing End-to-End Flow');
  
  logInfo('This test requires Analysis Worker to be running');
  logInfo('Submitting assessment and monitoring for completion...');
  
  try {
    // Submit assessment
    const submitResponse = await axios.post(`${ASSESSMENT_SERVICE_URL}/test/submit`, SAMPLE_ASSESSMENT);
    const { jobId } = submitResponse.data.data;
    
    logSuccess(`Assessment submitted. Job ID: ${jobId}`);
    logInfo('Waiting for processing to complete (max 2 minutes)...');
    
    // Poll for completion
    let attempts = 0;
    const maxAttempts = 24; // 2 minutes with 5-second intervals
    
    while (attempts < maxAttempts) {
      await delay(5000);
      attempts++;
      
      try {
        // Check job status in Assessment Service
        const statusResponse = await axios.get(`${ASSESSMENT_SERVICE_URL}/test/status/${jobId}`);
        const status = statusResponse.data.data.status;
        
        logInfo(`Attempt ${attempts}: Job status is ${status}`);
        
        if (status === 'completed') {
          logSuccess('End-to-end flow completed successfully!');
          logInfo(`Result ID: ${statusResponse.data.data.resultId}`);
          return true;
        } else if (status === 'failed') {
          logError('Job failed during processing');
          return false;
        }
        
      } catch (error) {
        logInfo(`Status check attempt ${attempts} failed, continuing...`);
      }
    }
    
    logError('End-to-end flow test timed out');
    return false;
    
  } catch (error) {
    logError('End-to-end flow test failed', error);
    return false;
  }
}

// Main test runner
async function runIntegrationTests() {
  console.log('🧪 Assessment-Archive Service Integration Tests');
  console.log('================================================');
  
  const results = {
    healthChecks: false,
    authentication: false,
    jobCreation: false,
    statusUpdates: false,
    endToEnd: false
  };
  
  // Run tests
  results.healthChecks = await testHealthChecks();
  
  if (results.healthChecks) {
    results.authentication = await testArchiveServiceAuthentication();
    
    if (results.authentication) {
      const jobId = await testJobCreationFlow();
      results.jobCreation = !!jobId;
      
      if (results.jobCreation) {
        results.statusUpdates = await testJobStatusUpdates(jobId);
        results.endToEnd = await testEndToEndFlow();
      }
    }
  }
  
  // Print summary
  console.log('\n📊 Test Results Summary');
  console.log('========================');
  console.log(`Health Checks: ${results.healthChecks ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Authentication: ${results.authentication ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Job Creation: ${results.jobCreation ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Status Updates: ${results.statusUpdates ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`End-to-End Flow: ${results.endToEnd ? '✅ PASS' : '❌ FAIL'}`);
  
  const passCount = Object.values(results).filter(Boolean).length;
  const totalCount = Object.keys(results).length;
  
  console.log(`\n🎯 Overall: ${passCount}/${totalCount} tests passed`);
  
  if (passCount === totalCount) {
    console.log('🎉 All tests passed! Integration fix is working correctly.');
  } else {
    console.log('⚠️  Some tests failed. Please check the implementation.');
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runIntegrationTests().catch(console.error);
}

module.exports = {
  runIntegrationTests,
  testHealthChecks,
  testArchiveServiceAuthentication,
  testJobCreationFlow,
  testJobStatusUpdates,
  testEndToEndFlow
};
