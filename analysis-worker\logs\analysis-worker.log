{"level":"info","maxRecords":50,"message":"UsageTracker initialized","retentionDays":7,"service":"analysis-worker","timestamp":"2025-07-19 14:14:08","version":"1.0.0"}
{"component":"usage_tracker","health":{"criticalAlerts":0,"isHealthy":false,"warningAlerts":1},"level":"info","message":"Usage monitoring report generated","metrics":{"alertCount":1,"bufferUtilization":16,"estimatedCost":0.000149,"successRate":75,"totalRequests":8,"totalTokens":300},"reportType":"usage_monitoring","service":"analysis-worker","structured":true,"timeframe":"daily","timestamp":"2025-07-19 14:14:09","version":"1.0.0"}
{"component":"usage_tracker","level":"info","message":"Real-time usage metrics","metrics":{"current":{"last24Hours":{"cost":0.000149,"requests":8,"successRate":75,"tokens":300},"lastHour":{"cost":0.000149,"requests":8,"successRate":75,"tokens":300}},"performance":{"averageResponseTime":1428,"errorRate":25,"tokenCountingLatency":214},"system":{"bufferUtilization":16,"isHealthy":false,"memoryUsageKB":2,"status":"healthy"},"timestamp":"2025-07-19T07:14:09.038Z"},"service":"analysis-worker","structured":true,"timestamp":"2025-07-19 14:14:09","version":"1.0.0"}
{"exportSize":2195,"format":"json","level":"info","message":"Usage data exported for monitoring","recordCount":8,"service":"analysis-worker","structured":true,"timeframe":"daily","timestamp":"2025-07-19 14:14:09","version":"1.0.0"}
{"level":"info","message":"Usage statistics reset successfully","service":"analysis-worker","timestamp":"2025-07-19 14:14:09","version":"1.0.0"}
{"level":"info","message":"UsageTracker destroyed successfully","service":"analysis-worker","timestamp":"2025-07-19 14:14:09","version":"1.0.0"}
{"level":"info","maxRecords":10000,"message":"UsageTracker initialized","retentionDays":30,"service":"analysis-worker","timestamp":"2025-07-20 04:44:11","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-20 04:44:11","version":"1.0.0","workerConcurrency":"10"}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-20 04:44:11","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-20 04:44:11","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-20 04:44:11","version":"1.0.0"}
{"concurrency":10,"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-20 04:44:11","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-20 04:44:11","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-20 04:44:41","version":"1.0.0"}
